/**
 * Representative<PERSON>ard Component
 * Creates a responsive card component for displaying representative information
 */

export class RepresentativeCard {
  constructor(representative) {
    this.representative = representative;
  }

  /**
   * Get initials from name for placeholder avatar
   */
  getInitials(name) {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }

  /**
   * Format phone number for display
   */
  formatPhone(phone) {
    // Remove country code for display
    return phone.replace('+233 ', '0');
  }

  /**
   * Get category badge class
   */
  getCategoryBadgeClass(category) {
    const badgeClasses = {
      'src-executives': 'badge-src-executives',
      'faculty-reps': 'badge-faculty-reps',
      'department-presidents': 'badge-department-presidents',
      'course-reps': 'badge-course-reps'
    };
    return badgeClasses[category] || 'badge-src-executives';
  }

  /**
   * Get category display name
   */
  getCategoryName(category) {
    const categoryNames = {
      'src-executives': 'SRC Executive',
      'faculty-reps': 'Faculty Rep',
      'department-presidents': 'Dept President',
      'course-reps': 'Course Rep'
    };
    return categoryNames[category] || 'Representative';
  }

  /**
   * Create profile image element
   */
  createProfileImage() {
    const { name, profileImage } = this.representative;
    
    // For now, use placeholder since we don't have actual images
    return `
      <div class="profile-placeholder">
        ${this.getInitials(name)}
      </div>
    `;
    
    // When actual images are available, use this:
    /*
    return `
      <img 
        src="${profileImage}" 
        alt="${name} profile photo" 
        class="profile-image"
        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
      >
      <div class="profile-placeholder" style="display: none;">
        ${this.getInitials(name)}
      </div>
    `;
    */
  }

  /**
   * Create contact buttons
   */
  createContactButtons() {
    const { email, phone, whatsapp } = this.representative;
    
    return `
      <div class="card-footer">
        <a href="mailto:${email}" class="contact-button contact-button-email" aria-label="Send email to ${this.representative.name}">
          <span class="contact-icon">✉️</span>
          Email
        </a>
        <a href="tel:${phone}" class="contact-button contact-button-phone" aria-label="Call ${this.representative.name}">
          <span class="contact-icon">📞</span>
          Call
        </a>
        ${whatsapp ? `
          <a href="https://wa.me/${whatsapp.replace(/\s+/g, '').replace('+', '')}" 
             class="contact-button contact-button-whatsapp" 
             target="_blank" 
             rel="noopener noreferrer"
             aria-label="WhatsApp ${this.representative.name}">
            <span class="contact-icon">💬</span>
            WhatsApp
          </a>
        ` : ''}
      </div>
    `;
  }

  /**
   * Create representative details
   */
  createDetails() {
    const { faculty, department, year } = this.representative;

    return `
      <div class="representative-details">
        <div class="detail-item">
          <span class="detail-icon">🏛️</span>
          <span>${faculty}</span>
        </div>
        <div class="detail-item">
          <span class="detail-icon">🏢</span>
          <span>${department}</span>
        </div>
        ${year ? `
          <div class="detail-item">
            <span class="detail-icon">📅</span>
            <span>Year ${year}</span>
          </div>
        ` : ''}
      </div>
    `;
  }

  /**
   * Render the complete card HTML
   */
  render() {
    const { id, name, role, category, bio } = this.representative;

    return `
      <article class="representative-card" data-id="${id}" data-category="${category}">
        <div class="card-header">
          ${this.createProfileImage()}
          <div class="card-info">
            <h3 class="representative-name">${name}</h3>
            <p class="representative-role">${role}</p>
            <p class="representative-department">${this.representative.faculty}</p>
          </div>
        </div>

        <div class="card-body">
          ${this.createDetails()}
          ${bio ? `<p class="representative-bio">${bio}</p>` : ''}
        </div>

        ${this.createContactButtons()}
      </article>
    `;
  }

  /**
   * Create DOM element from HTML string
   */
  createElement() {
    const template = document.createElement('template');
    template.innerHTML = this.render().trim();
    return template.content.firstChild;
  }
}

/**
 * RepresentativeGrid Component
 * Manages the grid layout and responsive behavior
 */
export class RepresentativeGrid {
  constructor(container) {
    this.container = container;
    this.representatives = [];
    this.filteredRepresentatives = [];
  }

  /**
   * Set representatives data
   */
  setRepresentatives(representatives) {
    this.representatives = representatives;
    this.filteredRepresentatives = [...representatives];
  }

  /**
   * Filter representatives based on criteria
   */
  filter(criteria) {
    this.filteredRepresentatives = this.representatives.filter(rep => {
      // Search filter
      if (criteria.search) {
        const searchTerm = criteria.search.toLowerCase();
        const searchableText = `${rep.name} ${rep.role} ${rep.faculty} ${rep.department}`.toLowerCase();
        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      // Category filter
      if (criteria.category && rep.category !== criteria.category) {
        return false;
      }

      // Department filter
      if (criteria.department && rep.department !== criteria.department) {
        return false;
      }

      // Year filter
      if (criteria.year && rep.year !== parseInt(criteria.year)) {
        return false;
      }

      return true;
    });

    this.render();
    return this.filteredRepresentatives.length;
  }

  /**
   * Sort representatives
   */
  sort(sortBy = 'name') {
    this.filteredRepresentatives.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'role':
          return a.role.localeCompare(b.role);
        case 'department':
          return a.department.localeCompare(b.department);
        case 'category':
          return a.category.localeCompare(b.category);
        default:
          return 0;
      }
    });

    this.render();
  }

  /**
   * Render the grid
   */
  render() {
    if (!this.container) return;

    // Clear existing content
    this.container.innerHTML = '';

    // Render filtered representatives
    this.filteredRepresentatives.forEach(representative => {
      const card = new RepresentativeCard(representative);
      const cardElement = card.createElement();
      this.container.appendChild(cardElement);
    });

    // Add animation class for smooth transitions
    this.container.classList.add('fade-in');
    setTimeout(() => {
      this.container.classList.remove('fade-in');
    }, 300);
  }

  /**
   * Get current count
   */
  getCount() {
    return this.filteredRepresentatives.length;
  }

  /**
   * Get total count
   */
  getTotalCount() {
    return this.representatives.length;
  }
}
