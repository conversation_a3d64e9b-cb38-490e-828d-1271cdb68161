#!/usr/bin/env node

/**
 * UMaT SRC Directory Deployment Script
 * Automates the deployment process with validation and optimization
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

class Deployer {
  constructor() {
    this.projectRoot = process.cwd();
    this.buildDir = path.join(this.projectRoot, 'dist');
    this.errors = [];
    this.warnings = [];
  }

  /**
   * Main deployment process
   */
  async deploy() {
    console.log('🚀 Starting UMaT SRC Directory Deployment...\n');

    try {
      await this.validateEnvironment();
      await this.validateData();
      await this.runTests();
      await this.buildProject();
      await this.optimizeAssets();
      await this.generateReport();
      
      console.log('✅ Deployment completed successfully!');
      console.log(`📁 Build files are in: ${this.buildDir}`);
      
    } catch (error) {
      console.error('❌ Deployment failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Validate deployment environment
   */
  async validateEnvironment() {
    console.log('🔍 Validating environment...');

    // Check Node.js version
    const nodeVersion = process.version;
    const requiredVersion = '16.0.0';
    if (this.compareVersions(nodeVersion.slice(1), requiredVersion) < 0) {
      throw new Error(`Node.js ${requiredVersion} or higher required. Current: ${nodeVersion}`);
    }

    // Check required files
    const requiredFiles = [
      'package.json',
      'index.html',
      'src/main.js',
      'src/data/representatives.json',
      'src/data/departments.json'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(path.join(this.projectRoot, file))) {
        throw new Error(`Required file missing: ${file}`);
      }
    }

    console.log('✅ Environment validation passed');
  }

  /**
   * Validate data integrity
   */
  async validateData() {
    console.log('📊 Validating data...');

    try {
      // Load and validate representatives data
      const repsData = JSON.parse(
        fs.readFileSync(path.join(this.projectRoot, 'src/data/representatives.json'), 'utf8')
      );

      // Load and validate departments data
      const deptsData = JSON.parse(
        fs.readFileSync(path.join(this.projectRoot, 'src/data/departments.json'), 'utf8')
      );

      // Basic validation
      if (!repsData.representatives || !Array.isArray(repsData.representatives)) {
        throw new Error('Invalid representatives data structure');
      }

      if (!deptsData.faculties || !Array.isArray(deptsData.faculties)) {
        throw new Error('Invalid departments data structure');
      }

      // Check for required fields
      const requiredFields = ['id', 'name', 'role', 'category', 'faculty', 'department', 'email', 'phone'];
      for (const rep of repsData.representatives) {
        for (const field of requiredFields) {
          if (!rep[field]) {
            this.warnings.push(`Representative ${rep.name || rep.id} missing field: ${field}`);
          }
        }
      }

      console.log(`✅ Data validation passed (${repsData.representatives.length} representatives)`);
      
      if (this.warnings.length > 0) {
        console.log(`⚠️  ${this.warnings.length} warnings found`);
      }

    } catch (error) {
      throw new Error(`Data validation failed: ${error.message}`);
    }
  }

  /**
   * Run basic tests
   */
  async runTests() {
    console.log('🧪 Running tests...');

    try {
      // Test HTML validity
      const htmlContent = fs.readFileSync(path.join(this.projectRoot, 'index.html'), 'utf8');
      if (!htmlContent.includes('<!DOCTYPE html>')) {
        this.warnings.push('HTML5 doctype not found');
      }

      // Test CSS validity
      const cssContent = fs.readFileSync(path.join(this.projectRoot, 'src/styles/main.css'), 'utf8');
      if (!cssContent.includes(':root')) {
        this.warnings.push('CSS custom properties not found');
      }

      // Test JavaScript modules
      const jsContent = fs.readFileSync(path.join(this.projectRoot, 'src/main.js'), 'utf8');
      if (!jsContent.includes('import')) {
        this.warnings.push('ES6 modules not detected');
      }

      console.log('✅ Basic tests passed');

    } catch (error) {
      throw new Error(`Tests failed: ${error.message}`);
    }
  }

  /**
   * Build the project
   */
  async buildProject() {
    console.log('🔨 Building project...');

    try {
      // Install dependencies if needed
      if (!fs.existsSync(path.join(this.projectRoot, 'node_modules'))) {
        console.log('📦 Installing dependencies...');
        execSync('npm install', { stdio: 'inherit' });
      }

      // Run build
      console.log('🏗️  Building for production...');
      execSync('npm run build', { stdio: 'inherit' });

      // Verify build output
      if (!fs.existsSync(this.buildDir)) {
        throw new Error('Build directory not created');
      }

      const buildFiles = fs.readdirSync(this.buildDir);
      if (buildFiles.length === 0) {
        throw new Error('Build directory is empty');
      }

      console.log('✅ Build completed successfully');

    } catch (error) {
      throw new Error(`Build failed: ${error.message}`);
    }
  }

  /**
   * Optimize assets
   */
  async optimizeAssets() {
    console.log('⚡ Optimizing assets...');

    try {
      // Check for large files
      const checkLargeFiles = (dir) => {
        const files = fs.readdirSync(dir, { withFileTypes: true });
        
        for (const file of files) {
          const filePath = path.join(dir, file.name);
          
          if (file.isDirectory()) {
            checkLargeFiles(filePath);
          } else {
            const stats = fs.statSync(filePath);
            const sizeInMB = stats.size / (1024 * 1024);
            
            if (sizeInMB > 1) {
              this.warnings.push(`Large file detected: ${file.name} (${sizeInMB.toFixed(2)}MB)`);
            }
          }
        }
      };

      if (fs.existsSync(this.buildDir)) {
        checkLargeFiles(this.buildDir);
      }

      console.log('✅ Asset optimization completed');

    } catch (error) {
      console.warn('⚠️  Asset optimization failed:', error.message);
    }
  }

  /**
   * Generate deployment report
   */
  async generateReport() {
    console.log('📋 Generating deployment report...');

    const report = {
      timestamp: new Date().toISOString(),
      version: this.getVersion(),
      environment: {
        node: process.version,
        platform: process.platform,
        arch: process.arch
      },
      build: {
        success: true,
        warnings: this.warnings.length,
        errors: this.errors.length
      },
      files: this.getBuildStats()
    };

    // Write report
    const reportPath = path.join(this.buildDir, 'deployment-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    // Write human-readable report
    const readableReport = this.generateReadableReport(report);
    const readableReportPath = path.join(this.buildDir, 'DEPLOYMENT_REPORT.md');
    fs.writeFileSync(readableReportPath, readableReport);

    console.log('✅ Deployment report generated');
  }

  /**
   * Get project version
   */
  getVersion() {
    try {
      const packageJson = JSON.parse(
        fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8')
      );
      return packageJson.version || '1.0.0';
    } catch {
      return '1.0.0';
    }
  }

  /**
   * Get build statistics
   */
  getBuildStats() {
    if (!fs.existsSync(this.buildDir)) return {};

    const stats = {};
    const files = fs.readdirSync(this.buildDir, { withFileTypes: true });

    for (const file of files) {
      if (file.isFile()) {
        const filePath = path.join(this.buildDir, file.name);
        const fileStats = fs.statSync(filePath);
        stats[file.name] = {
          size: fileStats.size,
          sizeFormatted: this.formatBytes(fileStats.size)
        };
      }
    }

    return stats;
  }

  /**
   * Generate human-readable report
   */
  generateReadableReport(report) {
    return `# Deployment Report

**Date:** ${new Date(report.timestamp).toLocaleString()}
**Version:** ${report.version}
**Status:** ${report.build.success ? '✅ Success' : '❌ Failed'}

## Environment
- Node.js: ${report.environment.node}
- Platform: ${report.environment.platform}
- Architecture: ${report.environment.arch}

## Build Summary
- Warnings: ${report.build.warnings}
- Errors: ${report.build.errors}

## Files Generated
${Object.entries(report.files).map(([name, stats]) => 
  `- ${name}: ${stats.sizeFormatted}`
).join('\n')}

${this.warnings.length > 0 ? `## Warnings\n${this.warnings.map(w => `- ${w}`).join('\n')}` : ''}

## Next Steps
1. Test the built application
2. Deploy to your web server
3. Update DNS if necessary
4. Monitor for issues

---
Generated by UMaT SRC Directory Deployment Script
`;
  }

  /**
   * Format bytes to human readable
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Compare version strings
   */
  compareVersions(a, b) {
    const aParts = a.split('.').map(Number);
    const bParts = b.split('.').map(Number);
    
    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
      const aPart = aParts[i] || 0;
      const bPart = bParts[i] || 0;
      
      if (aPart > bPart) return 1;
      if (aPart < bPart) return -1;
    }
    
    return 0;
  }
}

// Run deployment if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const deployer = new Deployer();
  deployer.deploy().catch(console.error);
}

export default Deployer;
