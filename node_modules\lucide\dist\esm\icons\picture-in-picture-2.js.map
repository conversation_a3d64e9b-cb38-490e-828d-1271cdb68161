{"version": 3, "file": "picture-in-picture-2.js", "sources": ["../../../src/icons/picture-in-picture-2.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name picture-in-picture-2\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgOVY2YTIgMiAwIDAgMC0yLTJINGEyIDIgMCAwIDAtMiAydjEwYzAgMS4xLjkgMiAyIDJoNCIgLz4KICA8cmVjdCB3aWR0aD0iMTAiIGhlaWdodD0iNyIgeD0iMTIiIHk9IjEzIiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/picture-in-picture-2\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst PictureInPicture2: IconNode = [\n  'svg',\n  defaultAttributes,\n  [\n    ['path', { d: 'M21 9V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10c0 1.1.9 2 2 2h4' }],\n    ['rect', { width: '10', height: '7', x: '12', y: '13', rx: '2' }],\n  ],\n];\n\nexport default PictureInPicture2;\n"], "names": [], "mappings": ";;;;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,iBAA8B,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAClC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACA,CAAA,CAAA;AAAA,CACE,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAA4D,CAAA,CAAA;AAAA,CAC1E,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAI,KAAK,CAAA;AAAA,CAClE,CAAA,CAAA;AACF,CAAA,CAAA;;"}