{"version": 3, "file": "slice.js", "sources": ["../../../src/icons/slice.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name slice\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOCAxNC02IDZoOXYtMyIgLz4KICA8cGF0aCBkPSJNMTguMzcgMy42MyA4IDE0bDMgM0wyMS4zNyA2LjYzYTIuMTIgMi4xMiAwIDEgMC0zLTNaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/slice\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst Slice: IconNode = [\n  'svg',\n  defaultAttributes,\n  [\n    ['path', { d: 'm8 14-6 6h9v-3' }],\n    ['path', { d: 'M18.37 3.63 8 14l3 3L21.37 6.63a2.12 2.12 0 1 0-3-3Z' }],\n  ],\n];\n\nexport default Slice;\n"], "names": [], "mappings": ";;;;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAkB,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CACtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACA,CAAA,CAAA;AAAA,CACE,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAkB,CAAA,CAAA;AAAA,CAChC,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAwD,CAAA;AAAA,CACxE,CAAA,CAAA;AACF,CAAA,CAAA;;"}