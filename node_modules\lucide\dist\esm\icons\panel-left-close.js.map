{"version": 3, "file": "panel-left-close.js", "sources": ["../../../src/icons/panel-left-close.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name panel-left-close\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik05IDN2MTgiIC8+CiAgPHBhdGggZD0ibTE2IDE1LTMtMyAzLTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/panel-left-close\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst PanelLeftClose: IconNode = [\n  'svg',\n  defaultAttributes,\n  [\n    ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2' }],\n    ['path', { d: 'M9 3v18' }],\n    ['path', { d: 'm16 15-3-3 3-3' }],\n  ],\n];\n\nexport default PanelLeftClose;\n"], "names": [], "mappings": ";;;;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,cAA2B,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAC/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACA,CAAA,CAAA;AAAA,CACE,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAI,KAAK,CAAA,CAAA;AAAA,CAC/D,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAW,CAAA,CAAA;AAAA,CACzB,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAkB,CAAA;AAAA,CAClC,CAAA,CAAA;AACF,CAAA,CAAA;;"}