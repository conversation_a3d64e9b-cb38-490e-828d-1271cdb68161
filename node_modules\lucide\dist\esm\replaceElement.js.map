{"version": 3, "file": "replaceElement.js", "sources": ["../../src/replaceElement.ts"], "sourcesContent": ["import createElement from './createElement';\nimport { Icons } from './types';\n\nexport type CustomAttrs = { [attr: string]: any };\n\n/**\n * Get the attributes of an HTML element.\n * @param {HTMLElement} element\n * @returns {Object}\n */\nexport const getAttrs = (element: Element): Record<string, string> =>\n  Array.from(element.attributes).reduce<Record<string, string>>((attrs, attr) => {\n    attrs[attr.name] = attr.value;\n    return attrs;\n  }, {});\n\n/**\n * Gets the classNames of an attributes Object.\n * @param {Object} attrs\n * @returns {Array}\n */\nexport const getClassNames = (attrs: Record<string, string> | string): string | string[] => {\n  if (typeof attrs === 'string') return attrs;\n  if (!attrs || !attrs.class) return '';\n  if (attrs.class && typeof attrs.class === 'string') {\n    return attrs.class.split(' ');\n  }\n  if (attrs.class && Array.isArray(attrs.class)) {\n    return attrs.class;\n  }\n  return '';\n};\n\n/**\n * Combines the classNames of array of classNames to a String\n * @param {array} arrayOfClassnames\n * @returns {string}\n */\nexport const combineClassNames = (arrayOfClassnames: (string | Record<string, string>)[]) => {\n  const classNameArray = arrayOfClassnames.flatMap(getClassNames);\n\n  return classNameArray\n    .map((classItem) => classItem.trim())\n    .filter(Boolean)\n    .filter((value, index, self) => self.indexOf(value) === index)\n    .join(' ');\n};\n\nconst toPascalCase = (string: string): string =>\n  string.replace(/(\\w)(\\w*)(_|-|\\s*)/g, (g0, g1, g2) => g1.toUpperCase() + g2.toLowerCase());\n\ninterface ReplaceElementOptions {\n  nameAttr: string;\n  icons: Icons;\n  attrs: Record<string, string>;\n}\n\n/**\n * ReplaceElement, replaces the given element with the created icon.\n * @param {HTMLElement} element\n * @param {{ nameAttr: string, icons: object, attrs: object }} options: { nameAttr, icons, attrs }\n * @returns {Function}\n */\nconst replaceElement = (element: Element, { nameAttr, icons, attrs }: ReplaceElementOptions) => {\n  const iconName = element.getAttribute(nameAttr);\n\n  if (iconName == null) return;\n\n  const ComponentName = toPascalCase(iconName);\n\n  const iconNode = icons[ComponentName];\n\n  if (!iconNode) {\n    return console.warn(\n      `${element.outerHTML} icon name was not found in the provided icons object.`,\n    );\n  }\n\n  const elementAttrs = getAttrs(element);\n  const [tag, iconAttributes, children] = iconNode;\n\n  const iconAttrs = {\n    ...iconAttributes,\n    'data-lucide': iconName,\n    ...attrs,\n    ...elementAttrs,\n  };\n\n  const classNames = combineClassNames(['lucide', `lucide-${iconName}`, elementAttrs, attrs]);\n\n  if (classNames) {\n    Object.assign(iconAttrs, {\n      class: classNames,\n    });\n  }\n\n  const svgElement = createElement([tag, iconAttrs, children]);\n\n  return element.parentNode?.replaceChild(svgElement, element);\n};\n\nexport default replaceElement;\n"], "names": [], "mappings": ";;;;;;;;;AAUa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAW,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KACvB,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,IAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAC7E,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAI,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACxB,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA;AACT,CAAA,CAAA,CAAG,EAAE,CAAA,CAAA;AAOM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAA,CAAA,CAAA,CAAA,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAC1F,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAU,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA;AACtC,CAAA,CAAI,IAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAS,IAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAO,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,EAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAAA,CAAA;AAClD,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAG,CAAA,CAAA,CAAA,CAAA;AAAA,CAC9B,CAAA,CAAA;AACA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA;AAC7C,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACf,CAAA,CAAA;AACA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACT,CAAA,CAAA;AAOa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAC3F,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAE9D,CAAA,CAAO,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACJ,IAAI,CAAC,SAAA,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAK,CAAA,CAAA,CAAA,CAAA,CAAC,CACnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACd,OAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,KAAK,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,KAAK,CAC5D,CAAA,IAAA,CAAK,CAAA,CAAA,CAAG,CAAA,CAAA;AACb,CAAA,CAAA;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CACpB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,OAAO,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAA,CAAA,CAAA;AAc3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,iBAAiB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA,CAAA,CAAE,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAC9F,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAE9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEtB,CAAA,CAAM,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,aAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA;AAE3C,CAAA,CAAM,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,MAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA,CAAA;AAEpC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAAA,CAAA;AACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACb,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,sDAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CAAA,CACtB,CAAA;AAAA,CACF,CAAA,CAAA;AAEA,CAAA,CAAM,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,SAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA;AACrC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAExC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAY,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CAChB,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACH,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACf,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACH,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CACL,CAAA;AAEA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,UAAU,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA;AAE1F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,UAAY,CAAA,CAAA,CAAA;AACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAO,SAAW,CAAA,CAAA,CAAA;AAAA,CACvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CAAA,CACR,CAAA,CAAA;AAAA,CACH,CAAA,CAAA;AAEA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA;AAE3D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAC7D,CAAA,CAAA;;"}