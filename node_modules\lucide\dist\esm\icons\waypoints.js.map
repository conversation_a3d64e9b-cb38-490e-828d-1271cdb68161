{"version": 3, "file": "waypoints.js", "sources": ["../../../src/icons/waypoints.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name waypoints\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjQuNSIgcj0iMi41IiAvPgogIDxwYXRoIGQ9Im0xMC4yIDYuMy0zLjkgMy45IiAvPgogIDxjaXJjbGUgY3g9IjQuNSIgY3k9IjEyIiByPSIyLjUiIC8+CiAgPHBhdGggZD0iTTcgMTJoMTAiIC8+CiAgPGNpcmNsZSBjeD0iMTkuNSIgY3k9IjEyIiByPSIyLjUiIC8+CiAgPHBhdGggZD0ibTEzLjggMTcuNyAzLjktMy45IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTkuNSIgcj0iMi41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/waypoints\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst Waypoints: IconNode = [\n  'svg',\n  defaultAttributes,\n  [\n    ['circle', { cx: '12', cy: '4.5', r: '2.5' }],\n    ['path', { d: 'm10.2 6.3-3.9 3.9' }],\n    ['circle', { cx: '4.5', cy: '12', r: '2.5' }],\n    ['path', { d: 'M7 12h10' }],\n    ['circle', { cx: '19.5', cy: '12', r: '2.5' }],\n    ['path', { d: 'm13.8 17.7 3.9-3.9' }],\n    ['circle', { cx: '12', cy: '19.5', r: '2.5' }],\n  ],\n];\n\nexport default Waypoints;\n"], "names": [], "mappings": ";;;;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAsB,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAC1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACE,CAAC,UAAU,CAAE,CAAA,CAAA,CAAA,EAAI,MAAM,CAAI,CAAA,CAAA,CAAA,KAAA,CAAO,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAA,CAAA;AAAA,CAC5C,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAqB,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACnC,CAAC,UAAU,CAAE,CAAA,CAAA,CAAA,EAAI,OAAO,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAA,CAAA;AAAA,CAC5C,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAY,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CAC1B,CAAC,UAAU,CAAE,CAAA,CAAA,CAAA,EAAI,QAAQ,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAA,CAAA;AAAA,CAC7C,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAsB,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACpC,CAAC,UAAU,CAAE,CAAA,CAAA,CAAA,EAAI,MAAM,CAAI,CAAA,CAAA,CAAA,MAAA,CAAQ,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAA;AAAA,CAC/C,CAAA,CAAA;AACF,CAAA,CAAA;;"}