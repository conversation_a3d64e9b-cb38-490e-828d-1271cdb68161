/**
 * UMaT SRC Representatives Directory
 * Main Application Entry Point
 */

import { RepresentativeCard } from './components/RepresentativeCard.js';
import { SearchFilter } from './scripts/SearchFilter.js';
import { responsiveUtils, lazyLoader } from './components/ResponsiveUtils.js';
import { navigationManager, categoryManager } from './scripts/NavigationManager.js';

class UMatSRCDirectory {
  constructor() {
    this.representatives = [];
    this.departments = [];
    this.searchFilter = null;
    this.filteredRepresentatives = [];

    this.elements = {
      searchInput: document.getElementById('search-input'),
      advancedSearchInput: document.getElementById('advanced-search-input'),
      departmentFilter: document.getElementById('department-filter'),
      yearFilter: document.getElementById('year-filter'),
      clearButton: document.getElementById('search-clear'),
      advancedClearButton: document.getElementById('advanced-search-clear'),
      resultsCounter: document.getElementById('results-count'),
      loadingState: document.getElementById('loading-state'),
      emptyState: document.getElementById('empty-state'),
      searchResultsGrid: document.getElementById('search-results-grid')
    };

    this.init();
  }

  /**
   * Initialize the application
   */
  async init() {
    try {
      // Show loading state
      this.showLoadingState();

      // Load data
      await this.loadData();

      // Initialize components
      this.initializeComponents();

      // Setup responsive behavior
      this.setupResponsiveBehavior();

      // Initial render
      this.renderRepresentatives();

      // Hide loading state
      this.hideLoadingState();

      console.log('UMaT SRC Directory initialized successfully');
    } catch (error) {
      console.error('Failed to initialize directory:', error);
      this.showErrorState(error.message);
    }
  }

  /**
   * Load data from JSON files
   */
  async loadData() {
    try {
      // Load representatives data
      const representativesResponse = await fetch('/src/data/representatives.json');
      if (!representativesResponse.ok) {
        throw new Error('Failed to load representatives data');
      }
      const representativesData = await representativesResponse.json();
      this.representatives = representativesData.representatives;

      // Load departments data
      const departmentsResponse = await fetch('/src/data/departments.json');
      if (!departmentsResponse.ok) {
        throw new Error('Failed to load departments data');
      }
      const departmentsData = await departmentsResponse.json();
      this.departments = departmentsData;

      console.log(`Loaded ${this.representatives.length} representatives`);
    } catch (error) {
      console.error('Error loading data:', error);
      throw error;
    }
  }

  /**
   * Initialize components
   */
  initializeComponents() {
    // Initialize navigation
    navigationManager.initFromHash();

    // Initialize search and filter for dashboard
    this.searchFilter = new SearchFilter({
      searchInput: this.elements.searchInput,
      departmentFilter: this.elements.departmentFilter,
      yearFilter: this.elements.yearFilter,
      clearButton: this.elements.clearButton,
      resultsCounter: this.elements.resultsCounter,
      onFilterChange: (filters) => this.handleDashboardFilterChange(filters)
    });

    // Initialize advanced search
    this.advancedSearchFilter = new SearchFilter({
      searchInput: this.elements.advancedSearchInput,
      clearButton: this.elements.advancedClearButton,
      onFilterChange: (filters) => this.handleAdvancedSearchChange(filters)
    });

    // Populate department filter
    this.searchFilter.populateDepartmentFilter(this.representatives);
  }

  /**
   * Setup responsive behavior
   */
  setupResponsiveBehavior() {
    // Optimize layout on breakpoint changes
    responsiveUtils.onBreakpointChange((newBreakpoint, oldBreakpoint) => {
      console.log(`Breakpoint changed: ${oldBreakpoint} → ${newBreakpoint}`);
      responsiveUtils.optimizeLayout(this.elements.representativesGrid);
    });

    // Initial layout optimization
    responsiveUtils.optimizeLayout(this.elements.representativesGrid);
  }

  /**
   * Handle dashboard filter changes
   */
  handleDashboardFilterChange(filters) {
    this.filteredRepresentatives = this.filterRepresentatives(this.representatives, filters);

    // Re-render with filtered data
    const counts = categoryManager.renderByCategory(this.filteredRepresentatives, RepresentativeCard);
    navigationManager.updateNavigationCounts(counts);

    const totalCount = this.filteredRepresentatives.length;
    this.searchFilter.updateResultsCounter(totalCount, this.representatives.length);

    // Show/hide empty state
    if (totalCount === 0) {
      this.showEmptyState();
    } else {
      this.hideEmptyState();
    }

    this.setupLazyLoading();
  }

  /**
   * Handle advanced search changes
   */
  handleAdvancedSearchChange(filters) {
    const searchResults = this.filterRepresentatives(this.representatives, filters);
    this.renderSearchResults(searchResults);
  }

  /**
   * Filter representatives based on criteria
   */
  filterRepresentatives(representatives, criteria) {
    return representatives.filter(rep => {
      // Search filter
      if (criteria.search) {
        const searchTerm = criteria.search.toLowerCase();
        const searchableText = `${rep.name} ${rep.role} ${rep.faculty} ${rep.department}`.toLowerCase();
        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      // Department filter
      if (criteria.department && rep.department !== criteria.department) {
        return false;
      }

      // Year filter
      if (criteria.year && rep.year !== parseInt(criteria.year)) {
        return false;
      }

      return true;
    });
  }

  /**
   * Render search results
   */
  renderSearchResults(results) {
    const container = this.elements.searchResultsGrid;
    if (!container) return;

    container.innerHTML = '';

    results.forEach(rep => {
      const card = new RepresentativeCard(rep);
      const cardElement = card.createElement();
      container.appendChild(cardElement);
    });

    this.setupLazyLoading();
  }

  /**
   * Render representatives
   */
  renderRepresentatives() {
    // Render by category
    const counts = categoryManager.renderByCategory(this.representatives, RepresentativeCard);

    // Update navigation counts
    navigationManager.updateNavigationCounts(counts);

    // Update results counter
    const totalCount = this.representatives.length;
    this.searchFilter.updateResultsCounter(totalCount, totalCount);

    this.setupLazyLoading();
  }

  /**
   * Setup lazy loading for images
   */
  setupLazyLoading() {
    const cards = this.elements.representativesGrid.querySelectorAll('.representative-card:not(.lazy-loaded)');
    cards.forEach(card => {
      lazyLoader.observe(card);
    });
  }

  /**
   * Show loading state
   */
  showLoadingState() {
    if (this.elements.loadingState) {
      this.elements.loadingState.style.display = 'flex';
    }
    if (this.elements.representativesGrid) {
      this.elements.representativesGrid.style.display = 'none';
    }
    if (this.elements.emptyState) {
      this.elements.emptyState.style.display = 'none';
    }
  }

  /**
   * Hide loading state
   */
  hideLoadingState() {
    if (this.elements.loadingState) {
      this.elements.loadingState.style.display = 'none';
    }
    if (this.elements.representativesGrid) {
      this.elements.representativesGrid.style.display = 'grid';
    }
  }

  /**
   * Show empty state
   */
  showEmptyState() {
    if (this.elements.emptyState) {
      this.elements.emptyState.style.display = 'flex';
    }
    if (this.elements.representativesGrid) {
      this.elements.representativesGrid.style.display = 'none';
    }
  }

  /**
   * Hide empty state
   */
  hideEmptyState() {
    if (this.elements.emptyState) {
      this.elements.emptyState.style.display = 'none';
    }
    if (this.elements.representativesGrid) {
      this.elements.representativesGrid.style.display = 'grid';
    }
  }

  /**
   * Show error state
   */
  showErrorState(message) {
    if (this.elements.loadingState) {
      this.elements.loadingState.innerHTML = `
        <div class="error-state">
          <div class="error-icon">⚠️</div>
          <h3>Error Loading Directory</h3>
          <p>${message}</p>
          <button onclick="location.reload()" class="retry-button">Retry</button>
        </div>
      `;
    }
  }

  /**
   * Export data (for admin use)
   */
  exportData(format = 'json') {
    const data = {
      representatives: this.representatives,
      departments: this.departments,
      exportDate: new Date().toISOString()
    };

    if (format === 'json') {
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'umat-src-directory.json';
      a.click();
      URL.revokeObjectURL(url);
    }
  }

  /**
   * Get statistics
   */
  getStatistics() {
    const stats = {
      total: this.representatives.length,
      byCategory: {},
      byFaculty: {},
      byDepartment: {},
      byYear: {}
    };

    this.representatives.forEach(rep => {
      // By category
      stats.byCategory[rep.category] = (stats.byCategory[rep.category] || 0) + 1;
      
      // By faculty
      stats.byFaculty[rep.faculty] = (stats.byFaculty[rep.faculty] || 0) + 1;
      
      // By department
      stats.byDepartment[rep.department] = (stats.byDepartment[rep.department] || 0) + 1;
      
      // By year (for course reps)
      if (rep.year) {
        stats.byYear[rep.year] = (stats.byYear[rep.year] || 0) + 1;
      }
    });

    return stats;
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.umatDirectory = new UMatSRCDirectory();
});

// Add some utility functions to window for debugging
window.exportDirectory = () => {
  if (window.umatDirectory) {
    window.umatDirectory.exportData();
  }
};

window.getDirectoryStats = () => {
  if (window.umatDirectory) {
    console.table(window.umatDirectory.getStatistics());
  }
};

// Service Worker registration (for future PWA features)
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    // Service worker registration would go here
    console.log('Service Worker support detected');
  });
}
