{"version": 3, "file": "percent.js", "sources": ["../../../src/icons/percent.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name percent\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTkiIHgyPSI1IiB5MT0iNSIgeTI9IjE5IiAvPgogIDxjaXJjbGUgY3g9IjYuNSIgY3k9IjYuNSIgcj0iMi41IiAvPgogIDxjaXJjbGUgY3g9IjE3LjUiIGN5PSIxNy41IiByPSIyLjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/percent\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst Percent: IconNode = [\n  'svg',\n  defaultAttributes,\n  [\n    ['line', { x1: '19', x2: '5', y1: '5', y2: '19' }],\n    ['circle', { cx: '6.5', cy: '6.5', r: '2.5' }],\n    ['circle', { cx: '17.5', cy: '17.5', r: '2.5' }],\n  ],\n];\n\nexport default Percent;\n"], "names": [], "mappings": ";;;;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,OAAoB,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CACxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACA,CAAA,CAAA;AAAA,CACE,CAAA,CAAA,CAAA,CAAC,MAAQ,CAAA,CAAA,CAAA,CAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAK,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACjD,CAAC,UAAU,CAAE,CAAA,CAAA,CAAA,EAAI,OAAO,CAAI,CAAA,CAAA,CAAA,KAAA,CAAO,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CAC7C,CAAC,UAAU,CAAE,CAAA,CAAA,CAAA,EAAI,QAAQ,CAAI,CAAA,CAAA,CAAA,MAAA,CAAQ,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAA;AAAA,CACjD,CAAA,CAAA;AACF,CAAA,CAAA;;"}