{"version": 3, "file": "maximize-2.js", "sources": ["../../../src/icons/maximize-2.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name maximize-2\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxNSAzIDIxIDMgMjEgOSIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSI5IDIxIDMgMjEgMyAxNSIgLz4KICA8bGluZSB4MT0iMjEiIHgyPSIxNCIgeTE9IjMiIHkyPSIxMCIgLz4KICA8bGluZSB4MT0iMyIgeDI9IjEwIiB5MT0iMjEiIHkyPSIxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/maximize-2\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst Maximize2: IconNode = [\n  'svg',\n  defaultAttributes,\n  [\n    ['polyline', { points: '15 3 21 3 21 9' }],\n    ['polyline', { points: '9 21 3 21 3 15' }],\n    ['line', { x1: '21', x2: '14', y1: '3', y2: '10' }],\n    ['line', { x1: '3', x2: '10', y1: '21', y2: '14' }],\n  ],\n];\n\nexport default Maximize2;\n"], "names": [], "mappings": ";;;;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAsB,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAC1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACA,CAAA,CAAA;AAAA,CACE,CAAA,CAAA,CAAA,CAAC,UAAA,CAAY,CAAA,CAAA,CAAE,MAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAkB,CAAA,CAAA;AAAA,CACzC,CAAA,CAAA,CAAA,CAAC,UAAA,CAAY,CAAA,CAAA,CAAE,MAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAkB,CAAA,CAAA;AAAA,CACzC,CAAA,CAAA,CAAA,CAAC,MAAQ,CAAA,CAAA,CAAA,CAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAK,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAA,CAAA;AAAA,CAClD,CAAA,CAAA,CAAA,CAAC,MAAQ,CAAA,CAAA,CAAA,CAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAA;AAAA,CACpD,CAAA,CAAA;AACF,CAAA,CAAA;;"}