/**
 * NavigationManager
 * Handles sidebar navigation, section switching, and mobile responsiveness
 */

export class NavigationManager {
  constructor() {
    this.currentSection = 'dashboard';
    this.isMobile = window.innerWidth < 1024;
    this.sidebarOpen = false;
    
    this.elements = {
      sidebar: document.getElementById('sidebar'),
      sidebarToggle: document.getElementById('sidebar-toggle'),
      sidebarOverlay: document.getElementById('sidebar-overlay'),
      navLinks: document.querySelectorAll('.nav-link'),
      contentSections: document.querySelectorAll('.content-section')
    };

    this.init();
  }

  /**
   * Initialize navigation
   */
  init() {
    this.setupEventListeners();
    this.setupResponsiveHandling();
    this.updateActiveSection('dashboard');
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Sidebar toggle
    if (this.elements.sidebarToggle) {
      this.elements.sidebarToggle.addEventListener('click', () => {
        this.toggleSidebar();
      });
    }

    // Sidebar overlay
    if (this.elements.sidebarOverlay) {
      this.elements.sidebarOverlay.addEventListener('click', () => {
        this.closeSidebar();
      });
    }

    // Navigation links
    this.elements.navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const section = link.getAttribute('data-section');
        this.navigateToSection(section);
      });
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.sidebarOpen) {
        this.closeSidebar();
      }
    });

    // Window resize
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }

  /**
   * Setup responsive handling
   */
  setupResponsiveHandling() {
    this.updateMobileState();
    
    if (!this.isMobile) {
      this.openSidebar();
    }
  }

  /**
   * Handle window resize
   */
  handleResize() {
    const wasMobile = this.isMobile;
    this.updateMobileState();

    if (wasMobile && !this.isMobile) {
      // Switched from mobile to desktop
      this.openSidebar();
      this.elements.sidebarOverlay.classList.remove('active');
    } else if (!wasMobile && this.isMobile) {
      // Switched from desktop to mobile
      this.closeSidebar();
    }
  }

  /**
   * Update mobile state
   */
  updateMobileState() {
    this.isMobile = window.innerWidth < 1024;
    document.body.classList.toggle('mobile', this.isMobile);
  }

  /**
   * Toggle sidebar
   */
  toggleSidebar() {
    if (this.sidebarOpen) {
      this.closeSidebar();
    } else {
      this.openSidebar();
    }
  }

  /**
   * Open sidebar
   */
  openSidebar() {
    this.sidebarOpen = true;
    this.elements.sidebar.classList.add('open');
    this.elements.sidebarToggle.classList.add('active');
    
    if (this.isMobile) {
      this.elements.sidebarOverlay.classList.add('active');
      document.body.style.overflow = 'hidden';
    }
  }

  /**
   * Close sidebar
   */
  closeSidebar() {
    this.sidebarOpen = false;
    this.elements.sidebar.classList.remove('open');
    this.elements.sidebarToggle.classList.remove('active');
    this.elements.sidebarOverlay.classList.remove('active');
    document.body.style.overflow = '';
  }

  /**
   * Navigate to section
   */
  navigateToSection(sectionId) {
    this.updateActiveSection(sectionId);
    
    if (this.isMobile) {
      this.closeSidebar();
    }

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // Update URL hash
    window.history.pushState(null, null, `#${sectionId}`);
  }

  /**
   * Update active section
   */
  updateActiveSection(sectionId) {
    this.currentSection = sectionId;

    // Update nav links
    this.elements.navLinks.forEach(link => {
      const linkSection = link.getAttribute('data-section');
      link.classList.toggle('active', linkSection === sectionId);
    });

    // Update content sections
    this.elements.contentSections.forEach(section => {
      const isActive = section.id === `${sectionId}-section` || 
                      (sectionId !== 'dashboard' && section.id === `${sectionId}-only`) ||
                      (sectionId === 'dashboard' && section.id === 'dashboard-section');
      
      section.classList.toggle('active', isActive);
    });
  }

  /**
   * Update navigation counts
   */
  updateNavigationCounts(counts) {
    Object.entries(counts).forEach(([category, count]) => {
      const countElement = document.getElementById(`${category}-count`);
      const displayCountElement = document.getElementById(`${category}-display-count`);
      
      if (countElement) {
        countElement.textContent = count;
      }
      
      if (displayCountElement) {
        displayCountElement.textContent = `(${count})`;
      }
    });
  }

  /**
   * Get current section
   */
  getCurrentSection() {
    return this.currentSection;
  }

  /**
   * Initialize from URL hash
   */
  initFromHash() {
    const hash = window.location.hash.slice(1);
    const validSections = ['dashboard', 'src-executives', 'faculty-reps', 'department-presidents', 'course-reps', 'search'];
    
    if (hash && validSections.includes(hash)) {
      this.updateActiveSection(hash);
    }
  }
}

/**
 * CategoryManager
 * Manages the categorized display of representatives
 */
export class CategoryManager {
  constructor() {
    this.categories = {
      'src-executives': {
        name: 'SRC Executives',
        icon: '👑',
        description: 'Student Representative Council Executive Members',
        gridId: 'src-executives-grid',
        onlyGridId: 'src-executives-only-grid'
      },
      'faculty-reps': {
        name: 'Faculty Representatives',
        icon: '🏛️',
        description: 'Representatives from each faculty',
        gridId: 'faculty-reps-grid',
        onlyGridId: 'faculty-reps-only-grid'
      },
      'department-presidents': {
        name: 'Department Presidents',
        icon: '🏢',
        description: 'Presidents of departmental student associations',
        gridId: 'department-presidents-grid',
        onlyGridId: 'department-presidents-only-grid'
      },
      'course-reps': {
        name: 'Course Representatives',
        icon: '🎓',
        description: 'Class representatives for each academic year',
        gridId: 'course-reps-grid',
        onlyGridId: 'course-reps-only-grid'
      }
    };
  }

  /**
   * Render representatives by category
   */
  renderByCategory(representatives, RepresentativeCard) {
    const categorizedReps = this.categorizeRepresentatives(representatives);
    
    // Render in dashboard sections
    Object.entries(this.categories).forEach(([categoryId, category]) => {
      const reps = categorizedReps[categoryId] || [];
      this.renderCategorySection(reps, category, RepresentativeCard);
    });

    // Render course reps with special grouping
    this.renderCourseRepsGrouped(categorizedReps['course-reps'] || [], RepresentativeCard);

    return this.getCategoryCounts(categorizedReps);
  }

  /**
   * Categorize representatives
   */
  categorizeRepresentatives(representatives) {
    const categorized = {};
    
    representatives.forEach(rep => {
      const category = rep.category;
      if (!categorized[category]) {
        categorized[category] = [];
      }
      categorized[category].push(rep);
    });

    return categorized;
  }

  /**
   * Render category section
   */
  renderCategorySection(representatives, category, RepresentativeCard) {
    const dashboardGrid = document.getElementById(category.gridId);
    const onlyGrid = document.getElementById(category.onlyGridId);

    if (dashboardGrid) {
      this.renderGrid(dashboardGrid, representatives, RepresentativeCard);
    }

    if (onlyGrid) {
      this.renderGrid(onlyGrid, representatives, RepresentativeCard);
    }
  }

  /**
   * Render grid
   */
  renderGrid(container, representatives, RepresentativeCard) {
    if (!container) return;

    container.innerHTML = '';
    
    representatives.forEach(rep => {
      const card = new RepresentativeCard(rep);
      const cardElement = card.createElement();
      container.appendChild(cardElement);
    });
  }

  /**
   * Render course reps with grouping
   */
  renderCourseRepsGrouped(courseReps, RepresentativeCard) {
    const container = document.getElementById('course-reps-container');
    const onlyContainer = document.getElementById('course-reps-only-container');

    if (!courseReps.length) return;

    const grouped = this.groupCourseReps(courseReps);
    const html = this.generateCourseRepsHTML(grouped, RepresentativeCard);

    if (container) {
      container.innerHTML = html;
    }

    if (onlyContainer) {
      onlyContainer.innerHTML = html;
    }
  }

  /**
   * Group course reps by faculty, department, and year
   */
  groupCourseReps(courseReps) {
    const grouped = {};

    courseReps.forEach(rep => {
      const faculty = rep.faculty;
      const department = rep.department;
      const year = rep.year;

      if (!grouped[faculty]) {
        grouped[faculty] = {};
      }

      if (!grouped[faculty][department]) {
        grouped[faculty][department] = {};
      }

      if (!grouped[faculty][department][year]) {
        grouped[faculty][department][year] = [];
      }

      grouped[faculty][department][year].push(rep);
    });

    return grouped;
  }

  /**
   * Generate course reps HTML
   */
  generateCourseRepsHTML(grouped, RepresentativeCard) {
    let html = '';

    Object.entries(grouped).forEach(([faculty, departments]) => {
      const facultyCount = Object.values(departments).reduce((total, dept) => 
        total + Object.values(dept).reduce((deptTotal, years) => 
          deptTotal + Object.values(years).reduce((yearTotal, reps) => yearTotal + reps.length, 0), 0), 0);

      html += `
        <div class="faculty-group">
          <div class="faculty-group-header">
            <h3 class="faculty-group-title">${faculty}</h3>
            <p class="faculty-group-count">${facultyCount} representatives</p>
          </div>
          <div class="department-groups">
      `;

      Object.entries(departments).forEach(([department, years]) => {
        const deptCount = Object.values(years).reduce((total, reps) => total + reps.length, 0);

        html += `
          <div class="department-group">
            <div class="department-group-header">
              <h4 class="department-group-title">${department}</h4>
              <p class="department-group-subtitle">${deptCount} representatives</p>
            </div>
            <div class="year-groups">
        `;

        Object.entries(years).forEach(([year, reps]) => {
          html += `
            <div class="year-group">
              <div class="year-group-header">
                <h5 class="year-group-title">Year ${year}</h5>
                <p class="year-group-count">${reps.length} representative${reps.length !== 1 ? 's' : ''}</p>
              </div>
              <div class="representatives-grid">
          `;

          reps.forEach(rep => {
            const card = new RepresentativeCard(rep);
            html += card.render();
          });

          html += `
              </div>
            </div>
          `;
        });

        html += `
            </div>
          </div>
        `;
      });

      html += `
          </div>
        </div>
      `;
    });

    return html;
  }

  /**
   * Get category counts
   */
  getCategoryCounts(categorizedReps) {
    const counts = {};
    
    Object.keys(this.categories).forEach(categoryId => {
      counts[categoryId] = (categorizedReps[categoryId] || []).length;
    });

    return counts;
  }
}

// Export singleton instances
export const navigationManager = new NavigationManager();
export const categoryManager = new CategoryManager();
