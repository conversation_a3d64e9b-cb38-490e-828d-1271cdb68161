{"version": 3, "file": "videotape.js", "sources": ["../../../src/icons/videotape.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name videotape\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0yIDhoMjAiIC8+CiAgPGNpcmNsZSBjeD0iOCIgY3k9IjE0IiByPSIyIiAvPgogIDxwYXRoIGQ9Ik04IDEyaDgiIC8+CiAgPGNpcmNsZSBjeD0iMTYiIGN5PSIxNCIgcj0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/videotape\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst Videotape: IconNode = [\n  'svg',\n  defaultAttributes,\n  [\n    ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2' }],\n    ['path', { d: 'M2 8h20' }],\n    ['circle', { cx: '8', cy: '14', r: '2' }],\n    ['path', { d: 'M8 12h8' }],\n    ['circle', { cx: '16', cy: '14', r: '2' }],\n  ],\n];\n\nexport default Videotape;\n"], "names": [], "mappings": ";;;;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAsB,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAC1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACA,CAAA,CAAA;AAAA,CACE,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAI,KAAK,CAAA,CAAA;AAAA,CAC/D,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAW,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACzB,CAAC,UAAU,CAAE,CAAA,CAAA,CAAA,EAAI,KAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA;AAAA,CACxC,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAW,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACzB,CAAC,UAAU,CAAE,CAAA,CAAA,CAAA,EAAI,MAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA;AAAA,CAC3C,CAAA,CAAA;AACF,CAAA,CAAA;;"}