<p align="center">
  <a href="https://github.com/lucide-icons/lucide#gh-light-mode-only">
    <img src="https://lucide.dev/package-logos/lucide.svg#gh-light-mode-only" alt="Lucide - Implementation of the lucide icon library for web applications." width="400">
  </a>
  <a href="https://github.com/lucide-icons/lucide#gh-dark-mode-only">
    <img src="https://lucide.dev/package-logos/dark/lucide.svg#gh-dark-mode-only" alt="Lucide - Implementation of the lucide icon library for web applications." width="400">
  </a>
</p>

# Lucide

Implementation of the lucide icon library for web applications.

## Installation

### Package Managers

```sh
npm install lucide
```

```sh
pnpm install lucide
```

```sh
yarn add lucide
```

### CDN

```html
<!-- Development version -->
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

<!-- Production version -->
<script src="https://unpkg.com/lucide@latest"></script>
```

## Documentation

For full documentation, visit [lucide.dev](https://lucide.dev/guide/packages/lucide)

## Community

Join the [Discord server](https://discord.gg/EH6nSts) to chat with the maintainers and other users.

## License

Lucide is licensed under the ISC license. See [LICENSE](https://lucide.dev/license).

## Sponsors

<a href="https://vercel.com?utm_source=lucide&utm_campaign=oss">
  <img src="https://lucide.dev/vercel.svg" alt="Powered by Vercel" width="200" />
</a>

<a href="https://www.digitalocean.com/?refcode=b0877a2caebd&utm_campaign=Referral_Invite&utm_medium=Referral_Program&utm_source=badge"><img src="https://lucide.dev/digitalocean.svg" width="200" alt="DigitalOcean Referral Badge" /></a>

### Awesome backer 🍺

<a href="https://www.scipress.io?utm_source=lucide"><img src="https://lucide.dev/sponsors/scipress.svg" width="180" alt="Scipress sponsor badge" /></a>
