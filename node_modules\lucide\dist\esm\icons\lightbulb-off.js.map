{"version": 3, "file": "lightbulb-off.js", "sources": ["../../../src/icons/lightbulb-off.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name lightbulb-off\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYuOCAxMS4yYy44LS45IDEuMi0yIDEuMi0zLjJhNiA2IDAgMCAwLTkuMy01IiAvPgogIDxwYXRoIGQ9Im0yIDIgMjAgMjAiIC8+CiAgPHBhdGggZD0iTTYuMyA2LjNhNC42NyA0LjY3IDAgMCAwIDEuMiA1LjJjLjcuNyAxLjMgMS41IDEuNSAyLjUiIC8+CiAgPHBhdGggZD0iTTkgMThoNiIgLz4KICA8cGF0aCBkPSJNMTAgMjJoNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/lightbulb-off\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst LightbulbOff: IconNode = [\n  'svg',\n  defaultAttributes,\n  [\n    ['path', { d: 'M16.8 11.2c.8-.9 1.2-2 1.2-3.2a6 6 0 0 0-9.3-5' }],\n    ['path', { d: 'm2 2 20 20' }],\n    ['path', { d: 'M6.3 6.3a4.67 4.67 0 0 0 1.2 5.2c.7.7 1.3 1.5 1.5 2.5' }],\n    ['path', { d: 'M9 18h6' }],\n    ['path', { d: 'M10 22h4' }],\n  ],\n];\n\nexport default LightbulbOff;\n"], "names": [], "mappings": ";;;;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,YAAyB,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAC7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACA,CAAA,CAAA;AAAA,CACE,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAkD,CAAA,CAAA;AAAA,CAChE,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAc,CAAA,CAAA;AAAA,CAC5B,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAyD,CAAA,CAAA;AAAA,CACvE,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAW,CAAA,CAAA;AAAA,CACzB,CAAA,CAAA,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAY,CAAA;AAAA,CAC5B,CAAA,CAAA;AACF,CAAA,CAAA;;"}