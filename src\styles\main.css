/* ===================================
   UMaT SRC Representatives Directory
   Main Stylesheet
   =================================== */

/* CSS Custom Properties (Design Tokens) */
:root {
  /* UMaT Brand Colors */
  --color-primary: #1B5E20;        /* UMaT Green (Dark) */
  --color-primary-light: #2E7D32;  /* UMaT Green (Medium) */
  --color-primary-lighter: #4CAF50; /* UMaT Green (Light) */
  --color-primary-lightest: #E8F5E8; /* UMaT Green (Very Light) */
  --color-secondary: #FF8F00;      /* UMaT Gold */
  --color-secondary-light: #FFB300; /* UMaT Gold (Light) */
  --color-accent: #FFC107;         /* UMaT Gold (Accent) */
  --color-cta-primary: #FF6B35;    /* Orange for primary CTAs */
  --color-cta-danger: #E53E3E;     /* Red for urgent CTAs */
  
  /* Neutral Colors */
  --color-white: #FFFFFF;
  --color-gray-50: #FAFAFA;
  --color-gray-100: #F5F5F5;
  --color-gray-200: #EEEEEE;
  --color-gray-300: #E0E0E0;
  --color-gray-400: #BDBDBD;
  --color-gray-500: #9E9E9E;
  --color-gray-600: #757575;
  --color-gray-700: #616161;
  --color-gray-800: #424242;
  --color-gray-900: #212121;
  
  /* Semantic Colors */
  --color-success: #4CAF50;
  --color-warning: #FF9800;
  --color-error: #F44336;
  --color-info: #2196F3;
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing */
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */
  
  /* Border Radius */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Breakpoints (for reference in media queries) */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-gray-800);
  background-color: var(--color-gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Focus Management for Accessibility */
:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Skip Link for Accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--color-white);
  padding: var(--spacing-2) var(--spacing-4);
  text-decoration: none;
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  z-index: 1000;
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* Layout Structure */
body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 999;
  transform: translateX(-100%);
  transition: transform var(--transition-normal);
}

.sidebar.open {
  transform: translateX(0);
}

.main-content {
  margin-top: 80px; /* Header height */
  margin-left: 0;
  transition: margin-left var(--transition-normal);
}

/* Mobile Responsive Adjustments */
@media (max-width: 640px) {
  .header {
    padding: var(--spacing-3) 0;
    height: 70px;
  }

  .main-content {
    margin-top: 70px;
    padding: var(--spacing-6) 0;
  }

  .sidebar {
    width: 100%;
    max-width: 320px;
  }

  .category-title {
    font-size: var(--font-size-xl);
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .contact-button {
    font-size: var(--font-size-xs);
    padding: var(--spacing-2) var(--spacing-3);
  }

  .representatives-grid {
    grid-template-columns: 1fr;
  }

  .filter-controls {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .representatives-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-controls {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .sidebar {
    transform: translateX(0);
    position: fixed;
  }

  .main-content {
    margin-left: 280px; /* Sidebar width */
  }

  .representatives-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .filter-controls {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .representatives-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Container */
.header-container {
  width: 100%;
  max-width: none;
  padding: 0 var(--spacing-4);
}

.content-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

@media (min-width: 640px) {
  .header-container,
  .content-container {
    padding: 0 var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .content-container {
    padding: 0 var(--spacing-8);
  }
}

/* Typography Utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Color Utilities */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-gray-600 { color: var(--color-gray-600); }
.text-gray-700 { color: var(--color-gray-700); }
.text-gray-800 { color: var(--color-gray-800); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-white { background-color: var(--color-white); }
.bg-gray-50 { background-color: var(--color-gray-50); }
.bg-gray-100 { background-color: var(--color-gray-100); }

/* Spacing Utilities */
.p-2 { padding: var(--spacing-2); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

.m-2 { margin: var(--spacing-2); }
.m-4 { margin: var(--spacing-4); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }

.mt-2 { margin-top: var(--spacing-2); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

.mb-2 { margin-bottom: var(--spacing-2); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

/* Border Radius Utilities */
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Transition Utilities */
.transition { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

/* ===================================
   COMPONENT STYLES
   =================================== */

/* Header */
.header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  color: var(--color-white);
  padding: var(--spacing-4) 0;
  box-shadow: var(--shadow-md);
  height: 80px;
  display: flex;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  width: 100%;
}

.sidebar-toggle {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: var(--color-white);
  margin: 2px 0;
  transition: all var(--transition-fast);
  border-radius: 1px;
}

.sidebar-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.sidebar-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.sidebar-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex: 1;
}

.logo {
  border-radius: var(--radius-lg);
  background: var(--color-white);
  padding: var(--spacing-2);
  flex-shrink: 0;
}

.title-section {
  text-align: left;
  min-width: 0;
}

.main-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-tight);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.subtitle {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  opacity: 0.9;
  line-height: var(--line-height-normal);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (min-width: 640px) {
  .main-title {
    font-size: var(--font-size-xl);
  }

  .subtitle {
    font-size: var(--font-size-base);
  }
}

@media (min-width: 1024px) {
  .sidebar-toggle {
    display: none;
  }

  .main-title {
    font-size: var(--font-size-2xl);
  }

  .subtitle {
    font-size: var(--font-size-lg);
  }
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: var(--color-white);
  border-right: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-lg);
  overflow-y: auto;
}

.sidebar-content {
  padding: var(--spacing-6) 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-4);
  padding: 0 var(--spacing-6);
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: var(--spacing-1);
}

.nav-item.nav-divider {
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--color-gray-200);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-6);
  color: var(--color-gray-700);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  background: var(--color-gray-50);
  color: var(--color-primary);
}

.nav-link.active {
  background: var(--color-primary);
  color: var(--color-white);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--color-secondary);
}

.nav-icon {
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.nav-text {
  flex: 1;
  font-size: var(--font-size-sm);
}

.nav-count {
  background: var(--color-gray-200);
  color: var(--color-gray-700);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  min-width: 20px;
  text-align: center;
}

.nav-link.active .nav-count {
  background: var(--color-white);
  color: var(--color-primary);
}

/* Emergency Contact */
.emergency-contact {
  margin-top: auto;
  padding: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
}

.emergency-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-4);
  text-align: center;
}

.emergency-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.emergency-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  text-align: center;
  justify-content: center;
}

.emergency-call {
  background: var(--color-cta-danger);
  color: var(--color-white);
}

.emergency-call:hover {
  background: #C53030;
  transform: translateY(-1px);
}

.emergency-email {
  background: var(--color-primary);
  color: var(--color-white);
}

.emergency-email:hover {
  background: var(--color-primary-light);
  transform: translateY(-1px);
}

.emergency-whatsapp {
  background: #25D366;
  color: var(--color-white);
}

.emergency-whatsapp:hover {
  background: #128C7E;
  transform: translateY(-1px);
}

.emergency-icon {
  font-size: var(--font-size-base);
}

/* Sidebar Footer */
.sidebar-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
  text-align: center;
}

.footer-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.footer-text {
  font-size: var(--font-size-xs);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
}

.footer-link {
  font-size: var(--font-size-xs);
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.footer-link:hover {
  text-decoration: underline;
}

/* Sidebar Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Main Content */
.main-content {
  padding: var(--spacing-8) 0;
  min-height: calc(100vh - 80px);
  flex: 1;
}

/* Content Sections */
.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

.section-header {
  margin-bottom: var(--spacing-8);
  text-align: center;
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-2);
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  max-width: 600px;
  margin: 0 auto;
}

/* Category Sections */
.categories-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-12);
}

.category-section {
  margin-bottom: var(--spacing-12);
}

.category-header {
  margin-bottom: var(--spacing-6);
  text-align: center;
  padding-bottom: var(--spacing-4);
  border-bottom: 2px solid var(--color-gray-200);
}

.category-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-2);
  flex-wrap: wrap;
}

.category-icon {
  font-size: var(--font-size-3xl);
}

.category-count {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-600);
  background: var(--color-gray-100);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
}

.category-description {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin: 0;
  max-width: 500px;
  margin: 0 auto;
}

/* Course Reps Container */
.course-reps-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

.faculty-group {
  background: var(--color-gray-50);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  border: 1px solid var(--color-gray-200);
}

.faculty-group-header {
  margin-bottom: var(--spacing-6);
  text-align: center;
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--color-gray-300);
}

.faculty-group-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--spacing-1);
}

.faculty-group-count {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
}

.department-groups {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.department-group {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  border: 1px solid var(--color-gray-200);
}

.department-group-header {
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--color-gray-200);
}

.department-group-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-1);
}

.department-group-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

.year-groups {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.year-group {
  border-left: 3px solid var(--color-primary);
  padding-left: var(--spacing-4);
}

.year-group-header {
  margin-bottom: var(--spacing-3);
}

.year-group-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
  margin-bottom: var(--spacing-1);
}

.year-group-count {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

/* Search Section */
.search-section {
  margin-bottom: var(--spacing-8);
}

.advanced-search {
  margin-bottom: var(--spacing-6);
}

.search-input.large {
  font-size: var(--font-size-lg);
  padding: var(--spacing-5) var(--spacing-6);
}

.search-results {
  margin-top: var(--spacing-6);
}

.search-container {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
}

.search-input-wrapper {
  position: relative;
  margin-bottom: var(--spacing-4);
}

.search-input {
  width: 100%;
  padding: var(--spacing-4) var(--spacing-5);
  border: 2px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: var(--font-family-primary);
  background: var(--color-white);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.search-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(27, 94, 32, 0.1);
  outline: none;
}

.search-input::placeholder {
  color: var(--color-gray-500);
}

.search-clear {
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-gray-500);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: color var(--transition-fast), background-color var(--transition-fast);
}

.search-clear:hover {
  color: var(--color-gray-700);
  background-color: var(--color-gray-100);
}

.filter-controls {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-3);
}

@media (min-width: 640px) {
  .filter-controls {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .filter-controls {
    grid-template-columns: repeat(3, 1fr);
  }
}

.filter-select {
  padding: var(--spacing-3) var(--spacing-4);
  border: 2px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-family: var(--font-family-primary);
  background: var(--color-white);
  cursor: pointer;
  transition: border-color var(--transition-fast);
}

.filter-select:focus {
  border-color: var(--color-primary);
  outline: none;
}

/* Results Summary */
.results-summary {
  margin-bottom: var(--spacing-6);
  text-align: center;
}

.results-text {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
}

/* Representatives Grid */
.representatives-section {
  position: relative;
}

.representatives-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

@media (min-width: 640px) {
  .representatives-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .representatives-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .representatives-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Representative Card */
.representative-card {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 320px;
}

.representative-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.representative-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-5);
  position: relative;
}

.profile-image {
  width: 70px;
  height: 70px;
  border-radius: var(--radius-full);
  object-fit: cover;
  border: 3px solid var(--color-gray-200);
  background: var(--color-gray-100);
  flex-shrink: 0;
}

.profile-placeholder {
  width: 70px;
  height: 70px;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--color-primary-lighter) 0%, var(--color-primary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  flex-shrink: 0;
  box-shadow: var(--shadow-md);
}

.card-info {
  flex: 1;
  min-width: 0;
  padding-top: var(--spacing-1);
}

.representative-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-2);
  line-height: var(--line-height-tight);
  word-wrap: break-word;
}

.representative-role {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
  margin-bottom: var(--spacing-2);
  line-height: var(--line-height-normal);
}

.representative-department {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-medium);
}

.card-body {
  flex: 1;
  margin-bottom: var(--spacing-5);
  display: flex;
  flex-direction: column;
}

.representative-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2) 0;
}

.detail-icon {
  width: 18px;
  height: 18px;
  color: var(--color-primary);
  flex-shrink: 0;
  font-size: var(--font-size-base);
}

.representative-bio {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  margin-top: auto;
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--color-gray-100);
  font-style: italic;
}

.card-footer {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
  margin-top: auto;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--color-gray-100);
}

.contact-button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  transition: all var(--transition-normal);
  border: 2px solid transparent;
  min-height: 44px;
  flex: 1;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.contact-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.contact-button-email {
  background: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
}

.contact-button-email:hover {
  background: var(--color-primary-light);
  border-color: var(--color-primary-light);
}

.contact-button-phone {
  background: var(--color-cta-danger);
  color: var(--color-white);
  border-color: var(--color-cta-danger);
}

.contact-button-phone:hover {
  background: #C53030;
  border-color: #C53030;
}

.contact-button-whatsapp {
  background: #25D366;
  color: var(--color-white);
  border-color: #25D366;
}

.contact-button-whatsapp:hover {
  background: #128C7E;
  border-color: #128C7E;
}

.contact-icon {
  width: 16px;
  height: 16px;
  font-size: var(--font-size-base);
}

/* Category Badge */
.category-badge {
  position: absolute;
  top: var(--spacing-6);
  right: var(--spacing-6);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: var(--shadow-sm);
  z-index: 10;
}

.badge-src-executives {
  background: var(--color-primary);
  color: var(--color-white);
}

.badge-faculty-reps {
  background: var(--color-secondary);
  color: var(--color-white);
}

.badge-department-presidents {
  background: var(--color-info);
  color: var(--color-white);
}

.badge-course-reps {
  background: var(--color-success);
  color: var(--color-white);
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-16) var(--spacing-4);
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-16) var(--spacing-4);
  text-align: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  background: var(--color-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-6);
}

.empty-icon .icon-users {
  width: 40px;
  height: 40px;
  color: var(--color-gray-400);
}

.empty-state h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.empty-state p {
  color: var(--color-gray-600);
  font-size: var(--font-size-base);
}

/* Footer */
.footer {
  background: var(--color-gray-800);
  color: var(--color-white);
  padding: var(--spacing-8) 0;
  margin-top: var(--spacing-16);
}

.footer-content {
  text-align: center;
}

.footer-content p {
  margin-bottom: var(--spacing-2);
}

.footer-note {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

/* Icons (Simple CSS-based icons) */
.icon-search::before { content: "🔍"; }
.icon-x::before { content: "✕"; }
.icon-users::before { content: "👥"; }
.icon-mail::before { content: "✉️"; }
.icon-phone::before { content: "📞"; }
.icon-message-circle::before { content: "💬"; }
.icon-building::before { content: "🏢"; }
.icon-graduation-cap::before { content: "🎓"; }
.icon-calendar::before { content: "📅"; }

/* Responsive Utilities */
.hidden { display: none !important; }
.block { display: block !important; }
.flex { display: flex !important; }
.grid { display: grid !important; }

@media (max-width: 639px) {
  .sm\\:hidden { display: none !important; }
  .sm\\:block { display: block !important; }
}

@media (min-width: 640px) {
  .sm\\:flex { display: flex !important; }
  .sm\\:grid { display: grid !important; }
}

@media (min-width: 768px) {
  .md\\:flex { display: flex !important; }
  .md\\:grid { display: grid !important; }
}

@media (min-width: 1024px) {
  .lg\\:flex { display: flex !important; }
  .lg\\:grid { display: grid !important; }
}

/* Accessibility Styles */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focused {
  outline: 2px solid var(--color-primary) !important;
  outline-offset: 2px !important;
}

/* High Contrast Mode */
.high-contrast {
  --color-primary: #000000;
  --color-secondary: #000000;
  --color-white: #FFFFFF;
  --color-gray-800: #000000;
  --color-gray-600: #000000;
  --color-gray-400: #666666;
  --color-gray-200: #CCCCCC;
  --color-gray-100: #EEEEEE;
}

.high-contrast .representative-card {
  border: 2px solid var(--color-gray-800);
}

.high-contrast .contact-button {
  border: 2px solid var(--color-gray-800);
}

/* Reduced Motion */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Error Messages */
.error-message {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
  display: block;
}

.error-state {
  text-align: center;
  padding: var(--spacing-16);
}

.error-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-4);
}

.retry-button {
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  cursor: pointer;
  margin-top: var(--spacing-4);
  transition: background-color var(--transition-fast);
}

.retry-button:hover {
  background: var(--color-primary-light);
}

/* Contact Feedback */
.contact-feedback {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-gray-800);
  color: var(--color-white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Touch Spacing */
.touch-spacing-sm .representative-card {
  margin-bottom: var(--spacing-4);
}

.touch-spacing-md .representative-card {
  margin-bottom: var(--spacing-6);
}

.touch-spacing-sm .contact-button {
  min-height: 44px;
  min-width: 44px;
}

.touch-spacing-md .contact-button {
  min-height: 48px;
  min-width: 48px;
}

/* Print Styles */
@media print {
  .header,
  .search-section,
  .footer {
    display: none;
  }

  .representative-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid var(--color-gray-300);
  }

  .contact-button {
    display: none;
  }

  .representative-card::after {
    content: "Email: " attr(data-email) " | Phone: " attr(data-phone);
    display: block;
    margin-top: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
  }
}
